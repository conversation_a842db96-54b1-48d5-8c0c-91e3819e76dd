分数管理器使用说明
==================

## 已完成的修改：

1. 创建了独立的分数管理脚本 ScoreManager.js
2. 修改了 GameManager.js，移除了自动加分逻辑
3. 修改了 Coin.js，让金币收集使用分数管理器

## 在场景中设置分数管理器：

### 步骤1：创建分数管理器节点
1. 在Canvas下创建一个新的空节点，命名为 "ScoreManager"
2. 给这个节点添加 ScoreManager 脚本组件

### 步骤2：设置分数显示
1. 找到场景中的分数显示Label（应该是"label"节点）
2. 将这个Label拖拽到ScoreManager组件的"Score Label"属性中

### 步骤3：连接GameManager
1. 选择Canvas节点（应该有GameManager组件）
2. 将ScoreManager节点拖拽到GameManager组件的"Score Manager"属性中

## 分数管理器功能特性：

### 基础功能：
- 只有吃到金币才会加分（每个金币默认10分）
- 不再基于距离自动加分
- 独立的分数显示管理

### 高级功能：
- 连击奖励：在2秒内连续收集金币可获得额外分数
- 最高分记录：自动保存和显示最高分
- 事件系统：可监听分数变化事件

### 可调整参数：
- coinValue: 每个金币的分值（默认10分）
- comboBonus: 是否启用连击奖励（默认启用）
- comboTimeWindow: 连击时间窗口（默认2秒）

## 测试方法：
1. 运行游戏
2. 不吃金币时分数应该保持不变
3. 吃到金币时分数增加
4. 连续快速吃金币时会有连击奖励

## 如果遇到问题：
1. 检查ScoreManager节点是否正确添加了ScoreManager脚本
2. 检查GameManager的scoreManager属性是否正确连接
3. 检查分数Label是否正确连接到ScoreManager
4. 查看控制台是否有错误信息
