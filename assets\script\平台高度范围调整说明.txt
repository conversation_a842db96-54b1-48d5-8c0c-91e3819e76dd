平台高度范围调整说明
====================

## 调整目标：
扩大平台生成的高度范围，让最高的平台可以到100，最低的可以到-200，增加游戏的挑战性和多样性。

## 具体修改：

### 1. calculatePlatformY 方法调整：

#### 安全地面高度：
**调整前：** [-130, -110, -90]
**调整后：** [-130, -110, -90, -70, -50]
- 稍微扩展了安全地面的高度范围
- 保持相对安全，便于玩家跳跃

#### 挑战地面高度：
**调整前：** [-150, -130, -100, -90]
**调整后：** [-200, -170, -150, -130, -100, -70, -40, -10, 20, 50, 80, 100]
- 大幅扩展高度范围：从-200到100
- 总共12个不同的高度选择
- 增加了高平台的挑战性

### 2. 预设平台高度变化调整：

#### 高度变化范围：
**调整前：** [-30, -10, 10, 30]
**调整后：** [-80, -50, -30, -10, 10, 30, 50, 80, 120]
- 大幅扩展变化范围
- 增加了更多的高度选择

#### 高度限制：
- 添加了边界限制：Math.max(-200, Math.min(100, randomHeight))
- 确保所有平台都在-200到100的范围内

## 预期效果：

### 1. 视觉多样性：
- 平台现在可以分布在从屏幕底部到顶部的整个范围
- 高度差异更加明显，视觉效果更丰富

### 2. 游戏挑战性：
- 高平台（50-100）需要更精确的跳跃
- 低平台（-150到-200）增加下落风险
- 高度差异大的连续平台增加操作难度

### 3. 游戏策略性：
- 玩家需要根据平台高度调整跳跃策略
- 高低平台的组合增加了路径选择的复杂性

## 高度分布说明：

### 挑战地面的12个高度级别：
- **极低区域：** -200, -170 (高风险区域)
- **低区域：** -150, -130, -100 (原有低度范围)
- **中低区域：** -70, -40 (过渡区域)
- **中区域：** -10, 20 (中等高度)
- **中高区域：** 50, 80 (较高平台)
- **高区域：** 100 (最高挑战平台)

### 安全地面的5个高度级别：
- 范围：-130 到 -50
- 保持在相对安全的中低区域
- 确保玩家有稳定的落脚点

## 测试建议：
1. 检查最高平台（100）是否在屏幕可见范围内
2. 确认最低平台（-200）不会让玩家直接掉出屏幕
3. 测试高度差异大的平台组合是否可跳跃
4. 验证游戏的整体挑战性是否合适
