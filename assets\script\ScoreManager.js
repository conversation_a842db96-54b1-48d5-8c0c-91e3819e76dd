cc.Class({
    extends: cc.Component,

    properties: {
        // 分数显示标签
        scoreLabel: {
            default: null,
            type: cc.Label,
            tooltip: "用于显示分数的Label组件"
        },

        // 每个金币的分值
        coinValue: {
            default: 10,
            tooltip: "每个金币的分值"
        },

        // 连击奖励
        comboBonus: {
            default: true,
            tooltip: "是否启用连击奖励"
        },

        // 连击时间窗口（秒）
        comboTimeWindow: {
            default: 2.0,
            tooltip: "连击时间窗口，在此时间内收集金币可获得连击奖励"
        }
    },

    onLoad() {
        // 初始化分数数据
        this.score = 0;
        this.comboCount = 0;
        this.lastCoinTime = 0;
        
        // 更新分数显示
        this.updateScoreDisplay();
        
        // 注册为全局分数管理器
        cc.game.scoreManager = this;
        
        console.log("分数管理器初始化完成");
    },

    start() {
        // 确保分数显示正确
        this.updateScoreDisplay();
    },

    // 添加分数（主要方法）
    addScore(points) {
        if (!points || points <= 0) return;
        
        let finalPoints = points;
        
        // 处理连击奖励
        if (this.comboBonus) {
            let currentTime = Date.now() / 1000; // 转换为秒
            
            // 检查是否在连击时间窗口内
            if (currentTime - this.lastCoinTime <= this.comboTimeWindow) {
                this.comboCount++;
                
                // 连击奖励：每连击一次增加额外分数
                if (this.comboCount > 1) {
                    let comboMultiplier = Math.min(this.comboCount * 0.5, 3.0); // 最大3倍奖励
                    finalPoints = Math.floor(points * (1 + comboMultiplier));
                    
                    console.log(`连击 x${this.comboCount}! 获得 ${finalPoints} 分 (基础: ${points})`);
                }
            } else {
                // 重置连击
                this.comboCount = 1;
            }
            
            this.lastCoinTime = currentTime;
        }
        
        // 增加分数
        this.score += finalPoints;
        
        // 更新显示
        this.updateScoreDisplay();
        
        // 触发分数变化事件（可供其他系统监听）
        this.node.emit('scoreChanged', this.score, finalPoints);
        
        return finalPoints;
    },

    // 添加金币分数（专门用于金币收集）
    addCoinScore() {
        return this.addScore(this.coinValue);
    },

    // 添加奖励分数（用于特殊奖励）
    addBonusScore(points) {
        this.score += points;
        this.updateScoreDisplay();
        
        console.log(`获得奖励分数: ${points}`);
        this.node.emit('bonusScoreAdded', points);
        
        return points;
    },

    // 更新分数显示
    updateScoreDisplay() {
        if (this.scoreLabel) {
            this.scoreLabel.string = "分数: " + this.score;
        }
    },

    // 获取当前分数
    getScore() {
        return this.score;
    },

    // 获取当前连击数
    getComboCount() {
        return this.comboCount;
    },

    // 重置分数
    resetScore() {
        this.score = 0;
        this.comboCount = 0;
        this.lastCoinTime = 0;
        this.updateScoreDisplay();
        
        console.log("分数已重置");
        this.node.emit('scoreReset');
    },

    // 设置分数显示标签
    setScoreLabel(label) {
        this.scoreLabel = label;
        this.updateScoreDisplay();
    },

    // 保存最高分（可选功能）
    saveHighScore() {
        let currentHighScore = cc.sys.localStorage.getItem('highScore') || 0;
        if (this.score > currentHighScore) {
            cc.sys.localStorage.setItem('highScore', this.score);
            console.log(`新纪录! 最高分: ${this.score}`);
            return true;
        }
        return false;
    },

    // 获取最高分
    getHighScore() {
        return parseInt(cc.sys.localStorage.getItem('highScore')) || 0;
    },

    // 游戏结束时调用
    onGameOver() {
        console.log(`游戏结束! 最终分数: ${this.score}`);
        
        // 保存最高分
        let isNewRecord = this.saveHighScore();
        
        // 触发游戏结束事件
        this.node.emit('gameOver', {
            finalScore: this.score,
            isNewRecord: isNewRecord,
            highScore: this.getHighScore()
        });
        
        return {
            finalScore: this.score,
            isNewRecord: isNewRecord,
            highScore: this.getHighScore()
        };
    },

    onDestroy() {
        // 清理全局引用
        if (cc.game.scoreManager === this) {
            cc.game.scoreManager = null;
        }
    }
});
