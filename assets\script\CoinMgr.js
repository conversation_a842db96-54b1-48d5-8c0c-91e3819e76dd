
cc.Class({
    extends: cc.Component,

    properties: {
        coinprefab: cc.Prefab,

        // 金币组生成间隔（秒）
        spawnInterval: {
            default: 3,
            tooltip: "金币组生成间隔时间"
        },

        // 每组金币数量
        coinsPerGroup: {
            default: 8,
            tooltip: "每组生成的金币数量"
        },

        // 金币之间的间距
        coinSpacing: {
            default: 60,
            tooltip: "同组金币之间的水平间距"
        },

        // 金币组的生成模式
        groupPattern: {
            default: 0,
            type: cc.Enum({
                HORIZONTAL: 0,    // 水平一排
                CURVE_UP: 1,      // 向上弧形
                CURVE_DOWN: 2,    // 向下弧形
                ZIGZAG: 3,        // 之字形
                RANDOM: 4         // 随机模式
            }),
            tooltip: "金币组的排列模式"
        }
    },

    // LIFE-CYCLE CALLBACKS:


    onLoad () {
          // 初始化金币移动速度
          this.moveSpeed = 200;

          // 开始生成金币组
          this.spawnCoinGroup();
          this.schedule(this.spawnCoinGroup, this.spawnInterval);
          console.log("金币管理器初始化完成");
    },

    start () {

    },

    // update (dt) {},
    spawnCoinGroup() {
        // 确定当前使用的模式
        let currentPattern = this.groupPattern;
        if (currentPattern === 4) { // RANDOM模式
            currentPattern = Math.floor(Math.random() * 4); // 随机选择0-3中的一种
        }

        // 计算起始位置
        let startX = cc.winSize.width / 2 + 100; // 从屏幕右侧开始
        
        // 查找平台管理器
        let platformManager = cc.find("Canvas/Platforms");
        if (!platformManager) {
            console.log("无法找到平台管理器，使用默认高度");
            // 如果找不到平台管理器，使用默认高度
            let baseY = 120 + Math.random() * 80; // 基础高度在120-200之间
            this.generateCoins(startX, baseY, currentPattern);
            return;
        }
        
        // 获取平台管理器组件
        let platformManagerComp = platformManager.getComponent('PlatformManager');
        if (!platformManagerComp) {
            console.log("无法找到PlatformManager组件，使用默认高度");
            let baseY = 120 + Math.random() * 80;
            this.generateCoins(startX, baseY, currentPattern);
            return;
        }
        
        // 找到适合放置金币的平台（在屏幕右侧的平台）
        let platformFound = false;
        let baseY = 0;
        let platformHeight = 0;
        
        // 尝试查找位于屏幕右侧的平台
        if (platformManagerComp.platforms) {
            for (let i = 0; i < platformManagerComp.platforms.length; i++) {
                let platform = platformManagerComp.platforms[i];
                // 检查平台是否在屏幕右侧附近
                if (platform.x > cc.winSize.width / 4 && platform.x < cc.winSize.width * 1.5) {
                    // 使用平台的高度位置加上一些偏移（放在平台上方）
                    platformFound = true;
                    platformHeight = platform.height || 20; // 默认高度为20
                    baseY = platform.y + (platformHeight / 2) + 25; // 平台顶部再上移一点点
                    break;
                }
            }
        }
        
        // 如果找不到合适的平台，使用随机高度
        if (!platformFound) {
            console.log("无法找到合适的平台，使用随机高度");
            baseY = 120 + Math.random() * 80;
        } else {
            console.log("找到平台，放置金币在高度:", baseY);
        }
        
        this.generateCoins(startX, baseY, currentPattern);
    },

    // 生成金币的辅助方法
    generateCoins(startX, baseY, currentPattern) {
        // 生成一组金币
        for (let i = 0; i < this.coinsPerGroup; i++) {
            let coin = cc.instantiate(this.coinprefab);
            this.node.addChild(coin);

            // 计算每个金币的位置
            let coinX = startX + i * this.coinSpacing;
            let coinY = this.calculateCoinY(i, baseY, currentPattern);

            coin.x = coinX;
            coin.y = coinY;

            // 设置金币的移动速度
            let coinScript = coin.getComponent('Coin');
            if (coinScript) {
                coinScript.moveSpeed = this.moveSpeed;
            }
        }

        // 金币组生成完成
    },

    // 根据模式计算金币的Y坐标
    calculateCoinY(index, baseY, pattern) {
        switch (pattern) {
            case 0: // HORIZONTAL - 水平一排
                return baseY;

            case 1: // CURVE_UP - 向上弧形
                let progress1 = index / (this.coinsPerGroup - 1);
                return baseY + Math.sin(progress1 * Math.PI) * 60;

            case 2: // CURVE_DOWN - 向下弧形
                let progress2 = index / (this.coinsPerGroup - 1);
                return baseY - Math.sin(progress2 * Math.PI) * 60;

            case 3: // ZIGZAG - 之字形
                return baseY + (index % 2 === 0 ? 0 : 40);

            default:
                return baseY;
        }
    },

    // 设置移动速度，由GameManager调用
    setMoveSpeed(speed) {
        this.moveSpeed = speed;

        // 更新所有现有金币的移动速度
        this.node.children.forEach(child => {
            if (child.name === "coin") {
                let coinScript = child.getComponent('Coin');
                if (coinScript) {
                    coinScript.moveSpeed = speed;
                }
            }
        });
    },

    // 重置金币管理器（游戏重新开始时调用）
    resetCoins() {
        // 清除所有现有金币
        this.node.children.forEach(child => {
            if (child.name === "coin") {
                child.destroy();
            }
        });

        // 重新开始生成金币组
        this.unschedule(this.spawnCoinGroup);
        this.spawnCoinGroup();
        this.schedule(this.spawnCoinGroup, this.spawnInterval);
    },

    // 设置金币组生成参数
    setCoinGroupSettings(coinsPerGroup, spacing, pattern) {
        if (coinsPerGroup !== undefined) this.coinsPerGroup = coinsPerGroup;
        if (spacing !== undefined) this.coinSpacing = spacing;
        if (pattern !== undefined) this.groupPattern = pattern;
    },
});
